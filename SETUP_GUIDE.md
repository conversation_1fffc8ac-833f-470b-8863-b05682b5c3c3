# Enhanced Waste Classification System - Setup Guide

## 1. General Python Code Execution

### Running Python Code
Python code can be executed in several ways:

1. **Interactive Python Shell**:
   ```bash
   python
   # or
   python3
   ```

2. **Execute Python Files**:
   ```bash
   python filename.py
   # or
   python3 filename.py
   ```

3. **Using Python IDEs**: PyCharm, VS Code, Jupyter Notebook, etc.

## 2. Python Virtual Environment Setup

### Why Use Virtual Environments?
Virtual environments isolate project dependencies, preventing conflicts between different projects.

### Creating and Activating Virtual Environments

#### Windows:
```cmd
# Create virtual environment
python -m venv waste_classifier_env

# Activate virtual environment
waste_classifier_env\Scripts\activate

# Deactivate (when done)
deactivate
```

#### Linux/Mac:
```bash
# Create virtual environment
python3 -m venv waste_classifier_env

# Activate virtual environment
source waste_classifier_env/bin/activate

# Deactivate (when done)
deactivate
```

### Alternative: Using conda
```bash
# Create environment
conda create -n waste_classifier python=3.9

# Activate environment
conda activate waste_classifier

# Deactivate
conda deactivate
```

## 3. Enhanced Waste Classifier Setup

### Prerequisites
- Python 3.8 or higher
- Webcam (for live detection features)
- At least 4GB RAM (8GB recommended for training)
- 2GB free disk space

### Step-by-Step Installation

#### Step 1: Clone/Download the Project
```bash
# If using git
git clone <repository-url>
cd <project-directory>

# Or download and extract the files to a folder
```

#### Step 2: Create Virtual Environment
**Windows:**
```cmd
python -m venv waste_classifier_env
waste_classifier_env\Scripts\activate
```

**Linux/Mac:**
```bash
python3 -m venv waste_classifier_env
source waste_classifier_env/bin/activate
```

#### Step 3: Install Dependencies
```bash
# Upgrade pip first
pip install --upgrade pip

# Install all required packages
pip install -r requirements.txt
```

#### Step 4: Verify Installation
```python
# Test script - save as test_installation.py
import tensorflow as tf
import cv2
import numpy as np
import tkinter as tk
import pyttsx3
import pygame
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from PIL import Image

print("✅ All dependencies installed successfully!")
print(f"TensorFlow version: {tf.__version__}")
print(f"OpenCV version: {cv2.__version__}")
print(f"NumPy version: {np.__version__}")

# Test GPU availability
if tf.config.list_physical_devices('GPU'):
    print("🎮 GPU acceleration available")
else:
    print("💻 Running on CPU (normal for most setups)")
```

Run the test:
```bash
python test_installation.py
```

### Step 5: Prepare Dataset (For Training)
Create a dataset folder structure:
```
dataset/
├── cardboard/     # 100+ images of cardboard waste
├── glass/         # 100+ images of glass waste  
├── metal/         # 100+ images of metal waste
├── paper/         # 100+ images of paper waste
├── plastic/       # 100+ images of plastic waste
└── trash/         # 100+ images of general trash
```

### Step 6: Run the Application
```bash
python enhanced_waste_classifier.py
```

## 4. Troubleshooting Common Issues

### Issue: TensorFlow Installation Problems
**Solution:**
```bash
# For CPU-only version
pip install tensorflow-cpu

# For GPU version (requires CUDA)
pip install tensorflow-gpu
```

### Issue: OpenCV Import Error
**Solution:**
```bash
pip uninstall opencv-python
pip install opencv-python-headless
```

### Issue: Audio/TTS Problems
**Windows:**
```bash
# Install additional audio dependencies
pip install pywin32
```

**Linux:**
```bash
# Install system audio libraries
sudo apt-get install espeak espeak-data libespeak1 libespeak-dev
sudo apt-get install pulseaudio
```

**Mac:**
```bash
# Install using homebrew
brew install espeak
```

### Issue: tkinter Not Found
**Linux:**
```bash
sudo apt-get install python3-tk
```

**Mac:**
```bash
# Usually included with Python, but if needed:
brew install python-tk
```

### Issue: Memory Errors During Training
- Reduce batch size in the code (line 422, 434)
- Use smaller image sizes
- Train on fewer images initially

## 5. Usage Instructions

### First Time Setup:
1. Launch the application: `python enhanced_waste_classifier.py`
2. Click "🧠 Train Model" and select your dataset folder
3. Wait for training to complete (5-15 minutes)
4. Models will be saved automatically

### Using the System:
1. **Upload Image**: Click "📁 Upload Image" to classify a single image
2. **Live Camera**: Click "📷 Start Camera" for live video feed
3. **Smart Scan**: Click "🔄 Smart Scan" for automatic detection
4. **Audio Control**: Click "🔊 Audio ON/OFF" to toggle sound

### Performance Tips:
- Use well-lit, clear images for better accuracy
- Ensure objects fill most of the image frame
- For live detection, hold objects steady for 3 seconds
- Train with diverse, high-quality dataset images

## 6. System Requirements

### Minimum Requirements:
- Python 3.8+
- 4GB RAM
- 2GB free disk space
- Webcam (optional, for live detection)

### Recommended Requirements:
- Python 3.9+
- 8GB RAM
- 4GB free disk space
- GPU with CUDA support (for faster training)
- High-resolution webcam

## 7. File Structure After Setup
```
project_folder/
├── enhanced_waste_classifier.py    # Main application
├── requirements.txt                # Dependencies list
├── SETUP_GUIDE.md                 # This guide
├── test_installation.py           # Installation test
├── enhanced_waste_classifier.h5   # Trained model (after training)
├── garbage_detector.h5            # Binary classifier (after training)
├── label_encoder.json             # Label encoder (after training)
└── dataset/                       # Your training images
    ├── cardboard/
    ├── glass/
    ├── metal/
    ├── paper/
    ├── plastic/
    └── trash/
```

## 8. Next Steps
1. Collect and organize your training dataset
2. Run the training process
3. Test the system with various waste items
4. Fine-tune by adding more training data if needed

For additional help or issues, check the error messages in the terminal and refer to the troubleshooting section above.
